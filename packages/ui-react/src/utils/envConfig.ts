/**
 * Environment configuration utilities for the ui-react package
 * Handles reading and parsing environment variables with proper defaults
 */

/**
 * Determines if testnet mode is enabled based on environment variables
 * 
 * @returns boolean - true if testnet mode is enabled, false otherwise (default)
 * 
 * The testnet mode controls whether to use test API endpoints instead of production endpoints.
 * This affects:
 * - Leaderboard API calls
 * - Freebet API calls  
 * - Other SDK-core API interactions
 * 
 * Environment variable: VITE_TESTNET_MODE
 * - Set to 'true' to enable testnet mode
 * - Any other value or undefined defaults to false (mainnet mode)
 * 
 * @example
 * ```ts
 * // In .env file:
 * // VITE_TESTNET_MODE=true
 * 
 * const isTestnet = getTestnetMode()
 * console.log(isTestnet) // true
 * ```
 */
export function getTestnetMode(): boolean {
  const testnetMode = import.meta.env.VITE_TESTNET_MODE
  
  // Only return true if explicitly set to 'true' (case-insensitive)
  // This ensures testnets are disabled by default for safety
  return testnetMode?.toLowerCase() === 'true'
}

/**
 * Get all environment configuration values
 * Useful for debugging or logging configuration state
 * 
 * @returns Object containing all relevant environment configuration
 */
export function getEnvConfig() {
  return {
    testnetMode: getTestnetMode(),
    affiliateAddress: import.meta.env.VITE_AFFILIATE_ADDRESS,
    baseUrl: import.meta.env.BASE_URL,
    rpcUrls: {
      base: import.meta.env.VITE_BASE_RPC_URL,
      polygon: import.meta.env.VITE_POLYGON_RPC_URL,
      avalanche: import.meta.env.VITE_AVALANCHE_RPC_URL,
      arbitrum: import.meta.env.VITE_ARBITRUM_RPC_URL,
    }
  }
}
